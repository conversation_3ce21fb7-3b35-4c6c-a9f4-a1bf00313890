import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/function_area.dart';
import '../utils/log_util.dart';

/// 功能区数据管理
/// 负责管理功能区列表的状态和本地存储
class FunctionAreaProvider extends ChangeNotifier {
  List<FunctionArea> _functionAreas = [];
  bool _isLoaded = false;
  bool _isLoading = false;
  String? _error;
  String? _currentCircleId;
  String? _currentTgid;

  // 常量
  static const String _keyFunctionAreas = 'function_areas_data';
  static const String _keyCurrentCircleId = 'function_areas_circle_id';
  static const String _keyCurrentTgid = 'function_areas_tgid';

  // Getters
  List<FunctionArea> get functionAreas => _functionAreas;
  bool get isLoaded => _isLoaded;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasData => _functionAreas.isNotEmpty;
  String? get currentCircleId => _currentCircleId;
  String? get currentTgid => _currentTgid;

  /// 初始化，从本地存储加载数据
  Future<void> initialize() async {
    if (_isLoaded) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 加载功能区数据
      final functionAreasData = prefs.getString(_keyFunctionAreas);
      if (functionAreasData != null) {
        try {
          final List<dynamic> jsonList = jsonDecode(functionAreasData);
          _functionAreas = jsonList
              .map((json) => FunctionArea.fromJson(json as Map<String, dynamic>))
              .toList();
        } catch (e) {
          LogUtil.e('解析功能区数据失败: $e', tag: 'FunctionAreaProvider');
          _functionAreas = [];
        }
      }
      
      // 加载当前圈子信息
      _currentCircleId = prefs.getString(_keyCurrentCircleId);
      _currentTgid = prefs.getString(_keyCurrentTgid);
      
      _isLoaded = true;
      
      LogUtil.d('功能区数据加载完成: ${_functionAreas.length} 个功能区, circleId=$_currentCircleId, tgid=$_currentTgid', 
                tag: 'FunctionAreaProvider');
      notifyListeners();
    } catch (e) {
      LogUtil.e('功能区数据加载失败: $e', tag: 'FunctionAreaProvider');
      _isLoaded = true;
      notifyListeners();
    }
  }

  /// 更新功能区数据
  Future<void> updateFunctionAreas(
    List<FunctionArea> functionAreas, {
    String? circleId,
    String? tgid,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      
      // 保存功能区数据
      final jsonList = functionAreas.map((area) => area.toJson()).toList();
      await prefs.setString(_keyFunctionAreas, jsonEncode(jsonList));
      
      // 保存当前圈子信息
      if (circleId != null) {
        await prefs.setString(_keyCurrentCircleId, circleId);
        _currentCircleId = circleId;
      }
      if (tgid != null) {
        await prefs.setString(_keyCurrentTgid, tgid);
        _currentTgid = tgid;
      }
      
      _functionAreas = functionAreas;
      _isLoading = false;
      
      LogUtil.d('功能区数据更新成功: ${_functionAreas.length} 个功能区', 
                tag: 'FunctionAreaProvider');
      notifyListeners();
    } catch (e) {
      _error = '保存功能区数据失败: $e';
      _isLoading = false;
      LogUtil.e('保存功能区数据失败: $e', tag: 'FunctionAreaProvider');
      notifyListeners();
    }
  }

  /// 检查是否需要重新加载数据（圈子或游戏发生变化）
  bool shouldReload(String? circleId, String? tgid) {
    return _currentCircleId != circleId || _currentTgid != tgid;
  }

  /// 清除数据
  Future<void> clearData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyFunctionAreas);
      await prefs.remove(_keyCurrentCircleId);
      await prefs.remove(_keyCurrentTgid);
      
      _functionAreas = [];
      _currentCircleId = null;
      _currentTgid = null;
      _error = null;
      _isLoading = false;
      
      LogUtil.d('功能区数据已清除', tag: 'FunctionAreaProvider');
      notifyListeners();
    } catch (e) {
      LogUtil.e('清除功能区数据失败: $e', tag: 'FunctionAreaProvider');
    }
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      if (loading) {
        _error = null;
      }
      notifyListeners();
    }
  }

  /// 设置错误状态
  void setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  /// 根据模块名查找功能区
  FunctionArea? findByModule(String areaModule) {
    try {
      return _functionAreas.firstWhere(
        (area) => area.areaModule == areaModule,
      );
    } catch (e) {
      return null;
    }
  }

  /// 根据名称查找功能区
  FunctionArea? findByName(String areaName) {
    try {
      return _functionAreas.firstWhere(
        (area) => area.areaName == areaName,
      );
    } catch (e) {
      return null;
    }
  }
}
