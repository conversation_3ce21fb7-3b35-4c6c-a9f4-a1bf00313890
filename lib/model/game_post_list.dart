
import 'dart:typed_data';

import 'package:dlyz_flutter/model/download_info.dart';

/// 游戏中心数据模型
class GamePostList {
  final int offset;
  final List<GameListDetail> record;
  final GameFloatConfig floatConfig;

  GamePostList({
    required this.offset,
    required this.record,
    required this.floatConfig
  });

  factory GamePostList.fromJson(Map<String, dynamic> json) {
    return GamePostList(
      offset: json['offset'] ?? 0,
      record: (json['record'] as List<dynamic>? ?? [])
          .map((item) => GameListDetail.fromJson(item))
          .toList(),
      floatConfig: GameFloatConfig.fromJson(json['float_config'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offset': offset,
      'record': record.map((post) => post.toJson()).toList(),
      'floatConfig': floatConfig.toJson()
    };
  }
}


/// 游戏中心详情数据模型
class GameListDetail {
  final int tGid;
  final String tGidName;
  final String tag;
  final String slogan;
  final String banner;
  final String bannerPreview;
  final String detailPage;
  final String icon;
  final GameFloatConfig floatConfig;
  DownloadInfo downloadInfo;
  GameActionConfig gameActionConfig;

  GameListDetail({
    required this.tGid,
    required this.tGidName,
    required this.tag,
    required this.slogan,
    required this.banner,
    required this.bannerPreview,
    required this.detailPage,
    required this.icon,
    required this.floatConfig,
    required this.downloadInfo,
    required this.gameActionConfig,
  });

  factory GameListDetail.fromJson(Map<String, dynamic> json) {
    return GameListDetail(
      tGid: json['tgid'] ?? 0,
      tGidName: json['tgid_name'] ?? '',
      tag: json['tag'] ?? '',
      slogan: json['slogan'] ?? '',
      banner: json['banner'] ?? '',
      bannerPreview: json['banner_preview'] ?? '',
      detailPage: json['detail_page'] ?? '',
      icon: json['icon'] ?? '',
      floatConfig: GameFloatConfig.fromJson(json['float_config'] ?? {}),
      downloadInfo: DownloadInfo(name: json['tgid_name'] ?? '', url: '', fileName: '', directoryPath: ''),
      gameActionConfig: GameActionConfig.fromJson(json['game_action_config'] ?? {})
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tGid': tGid,
      'tGidName': tGidName,
      'tag': tag,
      'slogan': slogan,
      'banner': banner,
      'bannerPreview': bannerPreview,
      'detailPage': detailPage,
      'icon': icon,
      'floatConfig': floatConfig.toJson(),
      'gameActionConfig': gameActionConfig.toJson()
    };
  }
}

/// 游戏操作配置
class GameActionConfig {
  final GameActionType actionType;
  final String directPackage;
  final String downloadUrl;
  final List<ChoiceOptions> choiceOptions;

  GameActionConfig({
    required this.actionType,
    required this.directPackage,
    required this.downloadUrl,
    required this.choiceOptions,
  });

  factory GameActionConfig.fromJson(Map<String, dynamic> json) {
    return GameActionConfig(
        actionType: GameActionType.fromString(json['action_type'] ?? ''),
        directPackage: json['direct_package'] ?? '',
        downloadUrl: json['download_url'] ?? '',
        choiceOptions: (json['choice_options'] as List<dynamic>? ?? [])
            .map((item) => ChoiceOptions.fromJson(item))
            .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'actionType': actionType.value,
      'directPackage': directPackage,
      'downloadUrl': downloadUrl,
      'choiceOptions': choiceOptions.map((post) => post.toJson()).toList(),
    };
  }
}


/// 选择选项
class ChoiceOptions {
  final String id;
  final PackageType packageType;
  final String title;
  final String packageName;
  final String wechatMiniProgramId;
  final String iconUrl;
  final String downloadUrl;
  final GameFloatConfig floatConfig;
  bool hasInstall;
  Uint8List? localIcon;

  ChoiceOptions({
    required this.id  ,
    required this.packageType,
    required this.title,
    required this.packageName,
    required this.wechatMiniProgramId,
    required this.iconUrl,
    required this.downloadUrl,
    required this.floatConfig,
    required this.hasInstall,
    this.localIcon
  });

  factory ChoiceOptions.fromJson(Map<String, dynamic> json) {
    return ChoiceOptions(
      id: json['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      packageType: PackageType.fromString(json['package_type'] ?? ''),
      title: json['title'] ?? '',
      packageName: json['package_name'] ?? '',
      wechatMiniProgramId: json['wechat_minigram_id'] ?? '',
      iconUrl: json['icon_url'] ?? '',
      downloadUrl: json['download_url'] ?? '',
      floatConfig: GameFloatConfig.fromJson(json['float_config'] ?? {}),
      hasInstall: json['has_install'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'packageType': packageType.value,
      'title': title,
      'packageName': packageName,
      'wechatMiniProgramId': wechatMiniProgramId,
      'iconUrl': iconUrl,
      'downloadUrl': downloadUrl,
      'floatConfig': floatConfig.toJson(),
      'hasInstall': hasInstall,
    };
  }
}

/// 悬浮信息配置
class GameFloatConfig {
  final bool floatMsg;

  GameFloatConfig({
    required this.floatMsg
  });

  factory GameFloatConfig.fromJson(Map<String, dynamic> json) {
    return GameFloatConfig(
        floatMsg: json['float_msg'] ?? false
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'floatMsg': floatMsg,
    };
  }
}

/// 游戏操作类型枚举
enum GameActionType {
  /// 直接打开
  directOpen('direct_open'),

  /// 显示选择
  showChoice('show_choice'),

  /// 需要下载
  downloadRequired('download_required'),

  /// iOS打开
  iosOpen('ios_open'),

  /// 未知状态
  unknown('');

  final String value;

  const GameActionType(this.value);

  /// 从字符串值创建枚举
  static GameActionType fromString(String value) {
    switch (value) {
      case 'direct_open':
        return GameActionType.directOpen;
      case 'show_choice':
        return GameActionType.showChoice;
      case 'download_required':
        return GameActionType.downloadRequired;
      case 'ios_open':
        return GameActionType.iosOpen;
      default:
        return GameActionType.unknown;
    }
  }

  /// 检查是否为直接打开类型
  bool isDirectOpen() => this == GameActionType.directOpen;

  /// 检查是否为显示选择类型
  bool isShowChoice() => this == GameActionType.showChoice;

  /// 检查是否为需要下载类型
  bool isDownloadRequired() => this == GameActionType.downloadRequired;

  /// 检查是否为iOS打开类型
  bool isIosOpen() => this == GameActionType.iosOpen;

  /// 检查是否为未知状态
  bool isUnknown() => this == GameActionType.unknown;
}



/// 游戏操作类型枚举
enum PackageType {
  /// 官方包
  official('official'),

  /// 微信小游戏
  wechat('wechat'),

  /// 渠道包
  otherPackage('other_package'),

  /// 未知状态
  unknown('');

  final String value;

  const PackageType(this.value);

  static PackageType fromString(String value) {
    switch (value) {
      case 'official':
        return PackageType.official;
      case 'wechat':
        return PackageType.wechat;
      case 'other_package':
        return PackageType.otherPackage;
      default:
        return PackageType.unknown;
    }
  }

  /// 检查是否为官方包
  bool isOfficial() => this == PackageType.official;

  /// 检查是否为微信小游戏
  bool isWeChat() => this == PackageType.wechat;

  /// 检查是否为渠道包
  bool isOtherPackage() => this == PackageType.otherPackage;

  /// 检查是否为未知状态
  bool isUnknown() => this == PackageType.unknown;
}