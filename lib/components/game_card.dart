
import 'dart:io';
import 'package:dlyz_flutter/components/select_game_bottom_dialog.dart';
import 'package:dlyz_flutter/components/video_player_card.dart';
import 'package:dlyz_flutter/model/game_card_info.dart';
import 'package:dlyz_flutter/model/game_post_list.dart';
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:flutter/material.dart';
import '../common/dl_color.dart';
import '../manager/game_data_manager.dart';
import '../model/download_info.dart';
import '../manager/channel_manager.dart';
import '../model/mini_program_info.dart';
import 'cache_image.dart';
import 'download_bottom_dialog.dart';

class GameCard extends StatefulWidget {
  final GameListDetail game;
  final VoidCallback? onTap;
  final VoidCallback? onDownload;
  final VoidCallback? onOpen;
  final VoidCallback? onGift;

  const GameCard({
    super.key,
    required this.game,
    this.onTap,
    this.onDownload,
    this.onOpen,
    this.onGift
  });

  @override
  State<GameCard> createState() => _GameCardState();
}

class _GameCardState extends State<GameCard> {

  @override
  void initState() {
    super.initState();
    _checkInstallation();
  }

  /// 检查是否已安装
  Future<void> _checkInstallation() async {
    if (widget.game.gameActionConfig.directPackage.isEmpty && widget.game.gameActionConfig.choiceOptions.isEmpty) return;

    try {
      String officialPackageName = widget.game.gameActionConfig.directPackage;
      if (officialPackageName.isEmpty) {
        officialPackageName = widget.game.gameActionConfig.choiceOptions.firstWhere((item) => item.packageType.isOfficial()).packageName;
      }
      final isInstalled = await ChannelManager().checkGameInstalled(packageName: officialPackageName);
      if (mounted) {
        setState(() {
          widget.game.downloadInfo.downloadStatusNotifier.value = isInstalled ? "启动" : widget.game.downloadInfo.downloadStatusNotifier.value;
        });
      }
    } catch (e) {
      debugPrint("检查游戏是否安装失败: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 游戏信息和下载按钮
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            ClipRRect(
                              child: ClipRRect(
                                borderRadius: const BorderRadius.all(Radius.circular(6.0)),
                                child: CachedImage(
                                  imageUrl: widget.game.icon,
                                  height: 32,
                                  width: 32,
                                ),
                              ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            SizedBox(
                              width: 140,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.game.tGidName,
                                    style: const TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    widget.game.tag,
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.grey[600],
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                        Row(
                          children: [
                            if (widget.game.floatConfig.floatMsg) ...[
                              _buildGiftBtn()
                            ],
                            SizedBox(width: 5),
                            _buildDownloadBtn(
                                game: widget.game,
                                onDownload: widget.onDownload
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                  Text(
                    widget.game.slogan,
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600
                    ),
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  GameDataManager().isVideoLink(widget.game.banner)
                  ? ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(12.0)),
                    child: VideoPlayerCard(
                        videoUrl: widget.game.banner,
                        coverUrl: widget.game.bannerPreview,
                        height: 180,
                        autoPlay: false,
                    ),
                  ) : ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(12.0)),
                    child: CachedImage(
                      imageUrl: widget.game.banner,
                      height: 180,
                    ),
                  ),
                  SizedBox(
                    height: 16,
                  ),
                ],
              ),
          ),
        )
      ),
    );
  }

  /// 打开游戏按钮
  Widget _openGameBtn(String btn, GameListDetail game) {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        ElevatedButton(
          onPressed: widget.onOpen,
          style: ElevatedButton.styleFrom(
              backgroundColor: DLColor.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              padding: EdgeInsets.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              side: BorderSide.none,
              minimumSize: Size(70, 30),
              maximumSize: Size(70, 30)
          ),
          child: Text(
            btn,
            style: TextStyle(
                color: Colors.white
            ),
          ),
        ),
      ],
    );
  }

  /// 下载按钮
  Widget _buildDownloadBtn({required GameListDetail game, required void Function()? onDownload}) {
    return ValueListenableBuilder<String>(
        valueListenable: game.downloadInfo.downloadStatusNotifier,
        builder: (context, value, child) {
          if (value.isEqualTo("启动")) {
            return _openGameBtn(value, game);
          } else if (value.isEqualTo("下载中")) {
            return ValueListenableBuilder<String>(
                valueListenable: game.downloadInfo.progressNotifier,
                builder: (context, progressValue, child) {
                  final progress = double.tryParse(progressValue.replaceAll('%', '')) ?? 0;

                  if (progress > 0) {
                    game.downloadInfo.cacheProgress = progress;
                  }

                  final displayProgress = progress > 0 ? progress : game.downloadInfo.cacheProgress ?? 0;
                  return Stack(
                    alignment: Alignment.center,
                    clipBehavior: Clip.none,
                    children: [
                      ElevatedButton(
                          onPressed: onDownload,
                          style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                              padding: EdgeInsets.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              side: BorderSide.none,
                              minimumSize: Size(70, 30),
                              maximumSize: Size(70, 30)
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: Stack(
                              children: [
                                LinearProgressIndicator(
                                  value: displayProgress / 100,
                                  backgroundColor: DLColor.downloadBackground,
                                  valueColor: const AlwaysStoppedAnimation<Color>(DLColor.primary),
                                  minHeight: 30.0,
                                ),
                                Center(
                                  child: Text(
                                    progressValue,
                                    style: TextStyle(
                                      color: Colors.white,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          )
                      ),
                    ],
                  );
                }
            );
          } else if (value.isEqualTo("继续")) {
            return Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                ElevatedButton(
                    onPressed: onDownload,
                    style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        padding: EdgeInsets.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        side: BorderSide.none,
                        minimumSize: Size(70, 30),
                        maximumSize: Size(70, 30)
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: Stack(
                        children: [
                          LinearProgressIndicator(
                            value: (game.downloadInfo.cacheProgress ?? 0.0) / 100,
                            backgroundColor: DLColor.downloadBackground,
                            valueColor: const AlwaysStoppedAnimation<Color>(DLColor.primary),
                            minHeight: 30.0,
                          ),
                          Center(
                            child: Text(
                              value,
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                ),
              ],
            );
          } else {
            return Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                ElevatedButton(
                    onPressed: onDownload,
                    style: ElevatedButton.styleFrom(
                        backgroundColor: value.isEqualTo("安装") ? DLColor.tagBackground : DLColor.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        padding: EdgeInsets.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        side: BorderSide.none,
                        minimumSize: Size(70, 30),
                        maximumSize: Size(70, 30)
                    ),
                    child: Text(
                      value,
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    )
                ),
              ],
            );
          }
        }
    );
  }

  /// 体验金按钮
  Widget _buildGiftBtn() {
    return ElevatedButton(
      onPressed: widget.onGift,
      style: ElevatedButton.styleFrom(
          backgroundColor: DLColor.tagBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          padding: EdgeInsets.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          side: BorderSide.none,
          minimumSize: Size(85, 30),
          maximumSize: Size(85, 30)
      ),
      child: Text(
        "领取体验金",
        style: TextStyle(
            color: Colors.white
        ),
      ),
    );
  }
}