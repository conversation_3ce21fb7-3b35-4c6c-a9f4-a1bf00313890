
import 'dart:io';
import 'dart:math';

import 'package:dlyz_flutter/components/download_bottom_dialog.dart';
import 'package:dlyz_flutter/components/game_card.dart';
import 'package:dlyz_flutter/components/refresh_header.dart';
import 'package:dlyz_flutter/manager/game_data_manager.dart';
import 'package:dlyz_flutter/model/game_card_info.dart';
import 'package:dlyz_flutter/providers/download_provider.dart';
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:dlyz_flutter/webview/js_bridge.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';

import '../../components/common_alert.dart';
import '../../components/select_game_bottom_dialog.dart';
import '../../manager/channel_manager.dart';
import '../../model/game_post_list.dart';
import '../../webview/webview_dialog.dart';

class GamesPage extends StatefulWidget {
  const GamesPage({super.key});

  @override
  State<GamesPage> createState() => _GamesState();
}

class _GamesState extends State<GamesPage> {

  // 刷新控制器
  EasyRefreshController controller = EasyRefreshController(
      controlFinishRefresh: true,
      controlFinishLoad: true
  );

  @override
  void initState() {
    super.initState();
    GameDataManager().requestGameList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: EasyRefresh(
          controller: controller,
          header: const CustomerHeader(),
          onRefresh: () async {
            GameDataManager().loadInstalledApps();
            GameDataManager().requestGameList();
            controller.finishRefresh();
            controller.resetFooter();
          },
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  color: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    "游戏中心",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600
                    ),
                  ),
                ),
                // 游戏列表
                Padding(
                  padding: const EdgeInsets.only(top: 1.0),
                  child: Consumer<GameDataManager>(
                      builder: (context, provider, child) {
                        return ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: provider.gameList.length,
                          itemBuilder: (context, index) {
                            final game = provider.gameList[index];
                            final downloadProvider = DownloadProvider();
                            return GameCard(
                                game: game,
                                onTap: () => showOfficialWeb(context, game, downloadProvider),
                                onDownload: () => clickDownload(context, game, downloadProvider),
                                onOpen: () => clickOpenGame(context, game, downloadProvider),
                                onGift: () => clickGift(context, game, downloadProvider),
                            );
                          },
                        );
                      }
                  ),
                ),
              ],
            ),
          ),
      ),
    );
  }

  /// 展示游戏官网
  void showOfficialWeb(BuildContext context, GameListDetail game, DownloadProvider provider) {
    if (game.detailPage.isEmpty) {
      Fluttertoast.showToast(
        msg: '当前游戏没有官网',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
      );
      return;
    }

    JSBridge jsBridge = JSBridge(context);
    jsBridge.registerHandler("openGame", (message) {
      clickDownload(context, game, provider);
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) {
      return WebViewDialog(
        url: game.detailPage,
        title: '官网',
        showToolBar: true,
        jsBridge: jsBridge,
      );
    });
  }

  /// 点击下载游戏
  void clickDownload(BuildContext context, GameListDetail game, DownloadProvider provider) async {
    if (Platform.isAndroid) {
      if (game.downloadInfo.downloadStatusNotifier.value.isEqualTo("继续")) {
        final decision = await DownloadProvider().handleDownloadWithDecision(game.downloadInfo);
        if (decision == DownloadDecision.requiresConfirmation && context.mounted) {
          CommonAlert.show(
              context: context,
              title: "温馨提示",
              content: "使用蜂窝数据下载【${game.tGidName}】官方包",
              cancelText: "稍后用WiFi下载",
              confirmText: "确定下载",
              onCancel: () async {
                await DownloadProvider().scheduleDownloadOnWifi(game.downloadInfo);
              },
              onConfirm: () async {
                await DownloadProvider().handleDownload(game.downloadInfo);
                if (context.mounted) {
                  DownloadBottomDialog.show(context: context, game: game);
                }
              }
          );
        } else if (decision == DownloadDecision.noNetwork) {
          Fluttertoast.showToast(
            msg: '当前无网络连接，请检查网络后重试',
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
          );
        } else {
          if (context.mounted) {
            DownloadBottomDialog.show(context: context, game: game);
          }
        }
      } else if (game.downloadInfo.downloadStatusNotifier.value.isEqualTo("安装")) {
        await DownloadProvider().handleDownload(game.downloadInfo);
      } else {
        DownloadBottomDialog.show(context: context, game: game);
      }
    } else if (Platform.isIOS) {
      if (game.gameActionConfig.actionType.isShowChoice()) {
        DownloadBottomDialog.show(context: context, game: game);
      } else {
        provider.handleDownload(game.downloadInfo);
      }
    }
  }


  /// 打开游戏
  void clickOpenGame(BuildContext context, GameListDetail game, DownloadProvider provider) {
    if (Platform.isAndroid) {
      final gameList = game.gameActionConfig.choiceOptions;
      if (game.gameActionConfig.actionType.isShowChoice()) {
        SelectGamePackageBottomDialog.show(
            context: context,
            packages: gameList,
            onConfirm: (game) {
              if (game.packageType.isWeChat()) {
                GameDataManager().requestJumpMiniProgram(game.wechatMiniProgramId);
              } else {
                ChannelManager().openInstalledGame(packageName: game.packageName);
              }
            }
        );
      } else {
        ChannelManager().openInstalledGame(packageName: game.gameActionConfig.directPackage);
      }
    } else if (Platform.isIOS) {
      if (game.gameActionConfig.actionType.isIosOpen()) {
        ChannelManager().openInstalledGame(packageName: game.gameActionConfig.directPackage);
      } else {
        DownloadBottomDialog.show(context: context, game: game);
      }
    }
  }

  /// 领取体验金
  void clickGift(BuildContext context, GameListDetail game, DownloadProvider provider) {
    JSBridge jsBridge = JSBridge(context);
    jsBridge.registerHandler("openGame", (message) {
      clickDownload(context, game, provider);
    });

    showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.transparent,
        builder: (context) {
          return WebViewDialog(
            url: game.detailPage,
            title: '官网',
            showToolBar: true,
            jsBridge: jsBridge,
          );
        });
  }

}