
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/model/game_center_pocket_data.dart';
import 'package:dlyz_flutter/model/game_post_list.dart';
import 'package:dlyz_flutter/model/mini_program_info.dart';
import 'package:dlyz_flutter/net/api/game_list_service.dart';
import 'package:dlyz_flutter/providers/download_provider.dart';
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:flutter/cupertino.dart';
import 'package:installed_apps/app_info.dart';
import 'package:installed_apps/installed_apps.dart';

import '../model/download_info.dart';
import '../model/game_card_info.dart';
import '../model/game_package_info.dart';
import '../utils/permission_utils.dart';

class GameDataManager extends ChangeNotifier {
  static final GameDataManager _instance = GameDataManager._internal();

  factory GameDataManager() {
    return _instance;
  }
  GameDataManager._internal();

  GameFloatConfig floatConfig = GameFloatConfig(floatMsg: false);

  // 存储已安装应用的列表
  List<AppInfo> _installedPackageInfo = [];
  List<String> _installedPackageNames = [];

  
  // 获取已安装应用列表的缓存
  List<AppInfo> get installedPackageInfo => List.unmodifiable(_installedPackageInfo);
  List<String> get installedPackageNames => List.unmodifiable(_installedPackageNames);

  final List<GameCardInfo> gameItems = [
    GameCardInfo(
        title: '港台云上城之歌',
        type: '冒险·动作·MMORPG',
        subtitle: '这一次，重新定义斗罗世界！',
        iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
        imageUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        videoUrl: 'https://imgcs.s98s2.com/aicc/video/1752075938385.mp4',
        packageName: 'com.tg.ysczg.tw',
        tag: '必得体验金',
        officialWeb: 'https://lhsj.37.com.cn/',
        miniProgram: '230',
        channelPackages: [
          GamePackageInfo(id: 1, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tt.lhgzs.asdv', miniProgram: '', isOfficial: true),
          GamePackageInfo(id: 2, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
          GamePackageInfo(id: 4, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
        ],
        downloadInfo: DownloadInfo(
            name: '港台云上城之歌',
            fileName: '',
            directoryPath: '',
            url: 'https://developer-mt.nofeba.com/media/DiskExtension/android_oversea_pack_output/2025_07_30/1000100_com_tg_ysczg_tw_seasdk_37000000_20250730_095649_1753840621.apk'
        )
    ),
    GameCardInfo(
        title: '斗罗大陆：猎魂世界',
        type: '卡牌·回合制·养成·多人联机',
        subtitle: '真正能打的斗罗大陆',
        iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
        imageUrl: 'https://imgcs.s98s2.com/image/webSite/article/1752072193000/MMO%E4%BB%A3%E8%A8%80%E4%BA%BA%E6%88%90%E9%BE%99.jpg',
        videoUrl: 'https://imgcs.s98s2.com/aicc/video/1752075938385.mp4',
        packageName: 'com.tt.lhgzs.dddllhsj',
        tag: '必得体验金',
        officialWeb: 'https://lhsj.37.com.cn/',
        miniProgram: '230',
        channelPackages: [
          GamePackageInfo(id: 1, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tt.lhgzs.dddllhsj', miniProgram: '', isOfficial: true),
          GamePackageInfo(id: 2, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
          GamePackageInfo(id: 3, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
          GamePackageInfo(id: 4, tag: '', iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png', packageName: 'com.tg.ysczg.tw', miniProgram: '', isOfficial: false),
        ],
        downloadInfo: DownloadInfo(
            name: '斗罗大陆：猎魂世界',
            url: 'https://dlcs.37tgy.com/upload/1_1023086_19077/douluodaluliehunshijie-pinzhuanguanggaosibu37zigengxin2_1001.apk'
        )
    ),
    GameCardInfo(
        title: '指尖像素城',
        type: '冒险·动作·MMORPG',
        subtitle: '1000万预约达成!',
        iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
        imageUrl: 'https://imgcs.s98s2.com/image/webSite/article/1752195478000/%E6%88%90%E5%9B%BE.jpg',
        videoUrl: '',
        packageName: 'com.gzqh.zjxsc.huawei',
        tag: '',
        officialWeb: '',
        miniProgram: '',
        channelPackages: [],
        downloadInfo: DownloadInfo(
            name: '指尖像素城',
            url: 'https://developer-mt.nofeba.com/media/android_mainland_pack_output/2025_06_23/huawei_20250618-dcb8ab4f-8196-4948-84f8-8f964f875134_20250623_1750652824.apk'
        )
    ),
    GameCardInfo(
        title: '斗罗大陆：猎魂世界',
        type: '冒险·动作·MMORPG',
        subtitle: '1000万预约达成!',
        iconUrl: 'https://developer.nofeba.com/media/config/12386a8a-d32e-11ef-9951-32994d297501.png',
        imageUrl: 'https://imgcs.s98s2.com/image/webSite/article/1752195478000/%E6%88%90%E5%9B%BE.jpg',
        videoUrl: '',
        packageName: 'com.gzqh.zjxsc.huawei',
        tag: 'ios',
        officialWeb: '',
        miniProgram: '',
        channelPackages: [],
        downloadInfo: DownloadInfo(
            name: '斗罗大陆：猎魂世界',
            url: 'https://apps.apple.com/hk/app/%E6%96%97%E7%BE%85%E5%A4%A7%E9%99%B8-%E7%8D%B5%E9%AD%82%E4%B8%96%E7%95%8C/id6738236172'
        )
    ),
  ];

  List<GameListDetail> gameList = [];

  Future<void> requestGameList() async {
    try {
      final gameService = GameListService();
      final response = await gameService.getGameList();
      final data = response.data;
      if (response.isSuccess && data != null) {
        reloadGameList(data.record);
        floatConfig = data.floatConfig;
        reloadInstalledGamesIcon();
        debugPrint('游戏中心列表加载成功');
      } else {
        reloadGameList(GameCenterPocketData().gameItems.record);
        floatConfig = GameCenterPocketData().gameItems.floatConfig;
        reloadInstalledGamesIcon();
        debugPrint('游戏中心列表加载失败: code=${response.code}, msg=${response.message}');
      }
    } catch (e) {
      debugPrint('游戏中心列表加载异常$e');
    } finally {
      notifyListeners();
    }
  }

  /// 根据flagId获取小程序跳转配置并跳转
  Future<void> requestJumpMiniProgram(int flagId, {String extra = ""}) async {
    try {
      final gameService = GameListService();
      final response = await gameService.getJumpMiniProgramConfig(flagId: flagId);
      if (response.isSuccess && response.data != null) {
        var skipType = response.data?.skipType;
        if (skipType != null && skipType != 0) {
          final miniProgramPath = response.data?.miniProgramPath ?? '';
          final schemeUrl = response.data?.schemeUrl ?? '';
          ChannelManager().jumpToMiniProgram(info: MiniProgramInfo(
              skipType: skipType,
              appId: response.data?.appId ?? '',
              miniProgramId: response.data?.miniProgramId ?? '',
              miniProgramPath: miniProgramPath + extra,
              schemeUrl: schemeUrl + extra
          ));
        }
      } else {
        debugPrint('跳转小程序失败: code=${response.code}, msg=${response.message}');
      }
    } catch (e) {
      debugPrint('跳转小程序异常$e');
    }
  }

  /// 获取已安装应用列表并存储包名
  Future<void> loadInstalledApps() async {
    try {
      _installedPackageInfo = await InstalledApps.getInstalledApps(true, true);
      _installedPackageNames = _installedPackageInfo.map((app) => app.packageName.trim()).toList();
    } catch (e) {
      debugPrint('获取已安装应用列表失败: $e');
      _installedPackageInfo = [];
      _installedPackageNames = [];
    }
  }

  /// 重载游戏本地Icon
  Future<void> reloadInstalledGamesIcon() async {
    try {
      for (GameListDetail tGame in gameList) {
        for (ChoiceOptions game in tGame.gameActionConfig.choiceOptions) {
          reloadInstalledGameIcon(game);
        }
      }
    } catch (e) {
      debugPrint('重载已安装游戏Icon失败: $e');
    }
  }

  Future<void> reloadInstalledGameIcon(ChoiceOptions game) async {
    try {
      AppInfo? exitPackage = findLocalPackage(game.packageName);
      game.localIcon = exitPackage?.icon;
    } catch (e) {
      debugPrint('重载已安装游戏Icon失败: $e');
    }
  }

  /// 刷新游戏列表数据
  Future<void> reloadGameList(List<GameListDetail> newGameList) async {
    try {
      if (gameList.isEmpty) {
        // 处理新的下载信息
        List<GameListDetail> list = newGameList;
        for (GameListDetail gameListDetail in list) {
          var gameName = gameListDetail.tGidName;
          var gameUrl = gameListDetail.gameActionConfig.downloadUrl;
          if (gameUrl.isEmpty && gameListDetail.gameActionConfig.choiceOptions.isNotEmpty) {
            ChoiceOptions? officialPackage;
            try {
              officialPackage = gameListDetail.gameActionConfig.choiceOptions.firstWhere((item) => item.packageType.isOfficial());
            } catch (e) {
              officialPackage = null;
            }
            if (officialPackage != null) {
              gameUrl = officialPackage.downloadUrl;
            } else {
              final pocketGame = findPocketGame(gameListDetail.tGid);
              if (pocketGame != null) {
                gameName = pocketGame.downloadInfo.name;
                gameUrl = pocketGame.downloadInfo.url;
              }
            }
          }
          gameListDetail.downloadInfo = DownloadInfo(name: gameName, url: gameUrl);
        }
        gameList = list;
        initGamesDownloadHandlers();
        reloadGamesDownloadTasks();
      } else {
        // 拿回旧的下载信息
        List<GameListDetail> list = newGameList;
        for (GameListDetail gameListDetail in list) {
          final tGid = gameListDetail.tGid;
          GameListDetail? existGameDetail = findExistGameDetail(tGid);
          if (existGameDetail != null) {
            gameListDetail.downloadInfo = existGameDetail.downloadInfo;
          }
        }
        gameList = list;
        reloadGamesDownloadTasks();
      }
    } catch (e) {
      debugPrint('重载游戏列表失败: $e');
    }
  }

  /// 初始化下载处理器
  Future<void> initGamesDownloadHandlers() async {
    try {
      for (final game in gameList) {
        debugPrint('jho 初始化游戏中心下载处理: ${game.downloadInfo.url}');
        DownloadProvider().initializeDownloadHandlers(game.downloadInfo);
      }
    } catch (e) {
      debugPrint('初始化游戏中心下载处理失败: $e');
    }
  }

  /// 重新加载下载任务
  Future<void> reloadGamesDownloadTasks() async {
    try {
      for (final game in gameList) {
        debugPrint('jho 重新加载下载任务: ${game.downloadInfo.url}');
        DownloadProvider().reloadDownloadTasks(game.downloadInfo);
      }
    } catch (e) {
      debugPrint('重新加载游戏中心下载任务失败: $e');
    }
  }

  /// 刷新游戏下载信息
  Future<void> refreshGamesDownloadInfo(int tGid, DownloadInfo downloadInfo) async {
    try {
      gameList.firstWhere((item) => item.tGid == tGid).downloadInfo = downloadInfo;
      DownloadProvider().initializeDownloadHandlers(downloadInfo);
    } catch (e) {
      debugPrint('刷新游戏下载信息失败：$e');
    }
  }

  /// 查询已存在游戏详情
  GameListDetail? findExistGameDetail(int tGid) {
    try {
      GameListDetail result = gameList.firstWhere((item) => item.tGid == tGid);
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 查询兜底游戏
  GameListDetail? findPocketGame(int tGid) {
    try {
      GameListDetail result = GameCenterPocketData().gameItems.record.firstWhere((item) => item.tGid == tGid);
      return result;
    } catch (e) {
      return null;
    }
  }

  AppInfo? findLocalPackage(String packageName) {
    try {
      final apps = _installedPackageInfo;
      AppInfo result = apps.firstWhere((item) => item.packageName.isEqualTo(packageName));
      return result;
    } catch (e) {
      return null;
    }
  }

  bool isVideoLink(String url) {
    if (url.isEmpty) return false;
    
    // 常见的视频文件扩展名
    final videoExtensions = [
      '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', 
      '.webm', '.m4v', '.3gp', '.3g2', '.mpg', '.mpeg',
      '.ts', '.mts', '.m2ts'
    ];
    
    // 转换为小写进行比较
    final lowerUrl = url.toLowerCase();
    
    // 检查URL是否以视频扩展名结尾
    for (final extension in videoExtensions) {
      if (lowerUrl.endsWith(extension)) {
        return true;
      }
    }
    
    // 检查URL中是否包含视频扩展名
    for (final extension in videoExtensions) {
      if (lowerUrl.contains(extension + '?') || lowerUrl.contains(extension + '#')) {
        return true;
      }
    }
    
    return false;
  }
}